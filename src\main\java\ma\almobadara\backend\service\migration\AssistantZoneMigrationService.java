package ma.almobadara.backend.service.migration;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.dto.administration.AssistantDTO;
import ma.almobadara.backend.dto.administration.EpsDto;
import ma.almobadara.backend.dto.administration.TagDTO;
import ma.almobadara.backend.dto.administration.ZoneDTO;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.exceptions.UserAlreadyExistsException;
import ma.almobadara.backend.exceptions.UserNotFoundException;
import ma.almobadara.backend.mapper.ZoneMapper;
import ma.almobadara.backend.model.administration.Zone;
import ma.almobadara.backend.repository.administration.TagRepository;
import ma.almobadara.backend.repository.administration.ZoneRepository;
import ma.almobadara.backend.service.administration.AssistantService;
import ma.almobadara.backend.service.administration.EpsService;
import ma.almobadara.backend.service.administration.ZoneService;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import ma.almobadara.backend.util.times.DateUtils;

import static ma.almobadara.backend.service.migration.util.UtilityClass.getCellValue;

@Service
@RequiredArgsConstructor
@Slf4j
public class AssistantZoneMigrationService {

    private final ZoneService zoneService;
    private final AssistantService assistantService;
    private final ZoneRepository zoneRepository;
    private final EpsService epsService;
    private final TagRepository tagRepository;
    private final ZoneMapper zoneMapper;

    public void migrateZoneAssistant(MultipartFile file) throws TechnicalException {
        log.info("Request to migrateZoneAssistant : {}", file);
        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);
            for (Row row : sheet) {
                try {

                if (row.getRowNum() == 0) continue;
                if (row.getCell(0) == null || row.getCell(0).getCellType() == CellType.BLANK) {
                    break;
                }
                Long epsId = null;
                String epsName = getCellValue(row, 5);

                if (epsName != null && epsName.toLowerCase().startsWith("eps")) {
                    EpsDto eps = createEpsDTO(row);
                    EpsDto savedEps = epsService.createEps(eps);
                    epsId = savedEps.getId();
                }

                ZoneDTO zone = createZoneDTO(row, epsId);
                ZoneDTO savedZone ;
                if(zoneRepository.findByCode(zone.getCode()).isEmpty()){
                    savedZone= zoneService.createZone(zone);

                }
                else{
                    Optional<Zone> zone1=zoneRepository.findByCode(zone.getCode());
                    savedZone=zoneMapper.zoneToZoneDTO(zone1.get());
                }


                AssistantDTO assistant = createAssistantDTO(row, savedZone.getId());
                try {
                    assistantService.createAssistant(assistant);
                } catch (UserAlreadyExistsException e) {
                    log.warn("Assistant with email {} already exists. Skipping row {}.", assistant.getEmail(), row.getRowNum());
                } catch (UserNotFoundException e) {
                    log.warn("Assistant with email {} not found in AD. Skipping row {}.", assistant.getEmail(), row.getRowNum());
                } catch (Exception e) {
                    log.error("Error creating assistant for row {}: {}", row.getRowNum(), e.getMessage());
                }
                }catch (TechnicalException e){
                    log.warn("error with this line {}", e.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("Error during migration: {}", e.getMessage());
            throw new TechnicalException("Error during migration: " + e.getMessage());
        }
    }


    private EpsDto createEpsDTO(Row row) {
        EpsDto eps = new EpsDto();
        eps.setName(getCellValue(row, 4));
        eps.setNameAr(getCellValue(row, 5));
        eps.setAddress("À compléter");
        eps.setAddressAr("قيد الإكمال");
        eps.setTags(buildTags());
        return eps;
    }
    private List<TagDTO> buildTags() {
        List<TagDTO> tags = new ArrayList<>();
        tagRepository.findByNameAndType("migration", "metier").ifPresent(tag -> tags.add(TagDTO.builder().id(tag.getId()).name(tag.getName()).build()));
        tagRepository.findByNameAndType("à_compléter", "metier").ifPresent(tag -> tags.add(TagDTO.builder().id(tag.getId()).name(tag.getName()).build()));
        return tags;
    }
    private ZoneDTO createZoneDTO(Row row, Long epsId) {
        ZoneDTO zone = new ZoneDTO();
        zone.setEpsId(epsId);
        zone.setCode(getCellValue(row,4));
        zone.setName(getCellValue(row, 5));
        zone.setNameAr(getCellValue(row, 6));
         zone.setOldZone(true);
        return zone;
    }

    private AssistantDTO createAssistantDTO(Row row, Long zoneId) throws TechnicalException {
        AssistantDTO assistant = new AssistantDTO();
        assistant.setZoneId(zoneId);
        String fullName = getCellValue(row, 1);
        if (fullName != null) {
            String[] nameParts = fullName.split(" ", 2);
            assistant.setFirstName(nameParts[0]);
            assistant.setLastName(nameParts.length > 1 ? nameParts[1] : "");
        }
        assistant.setCinNumber(getCellValue(row, 3));
        assistant.setEmail(getCellValue(row, 7));
        assistant.setDateAffectationToZone(getDateCellValue(row));
        assistant.setPhone(getCellValue(row, 11));
        assistant.setOldAssistant(true);
        return assistant;
    }

    private String getCellValue(Row row, int cellIndex) {
        try {
            return row.getCell(cellIndex) != null ? row.getCell(cellIndex).getStringCellValue().trim() : null;
        } catch (Exception e) {
            log.error("Error reading cell at row {} and column {}: {}", row.getRowNum(), cellIndex, e.getMessage());
            return null;
        }
    }

    private LocalDate getDateCellValue(Row row) throws TechnicalException {
        try {
            if (row.getCell(8) == null || row.getCell(8).getCellType() == CellType.BLANK) {
                return null;
            }
            if (row.getCell(8).getCellType() == CellType.STRING) {
                String dateStr = row.getCell(8).getStringCellValue().trim();
                if (dateStr.isEmpty()) return null;
                return DateUtils.parseLocalDateFromString(dateStr);
            } else if (row.getCell(8).getCellType() == CellType.NUMERIC) {
                // fallback for Excel date type
                return row.getCell(8).getDateCellValue() != null
                        ? row.getCell(8).getDateCellValue().toInstant().atZone(ZoneId.systemDefault()).toLocalDate()
                        : null;
            } else {
                throw new TechnicalException("Unsupported cell type for date at row " + row.getRowNum());
            }
        } catch (Exception e) {
            String errorMessage = String.format("Error reading date at row %d and column 8: %s",
                    row.getRowNum(), e.getMessage());
            log.error(errorMessage);
            throw new TechnicalException(errorMessage);
        }
    }
}

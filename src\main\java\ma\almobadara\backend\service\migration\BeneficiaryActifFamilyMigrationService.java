package ma.almobadara.backend.service.migration;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.clients.RefFeignClient;
import ma.almobadara.backend.dto.administration.TagDTO;
import ma.almobadara.backend.dto.beneficiary.BeneficiaryAddDTO;
import ma.almobadara.backend.dto.beneficiary.BeneficiaryDTO;
import ma.almobadara.backend.dto.beneficiary.BeneficiaryEducation;
import ma.almobadara.backend.dto.beneficiary.EducationDTO;
import ma.almobadara.backend.dto.family.AddedFamilyMemberResponse;
import ma.almobadara.backend.dto.family.FamilyMemberAddDTO;
import ma.almobadara.backend.dto.referentiel.*;
import ma.almobadara.backend.exceptions.FunctionalException;
import ma.almobadara.backend.exceptions.TechnicalException;
import ma.almobadara.backend.model.beneficiary.Beneficiary;
import ma.almobadara.backend.model.beneficiary.Person;
import ma.almobadara.backend.model.family.FamilyMember;
import ma.almobadara.backend.repository.administration.TagRepository;
import ma.almobadara.backend.repository.beneficiary.BeneficiaryRepository;
import ma.almobadara.backend.repository.beneficiary.PersonRepository;
import ma.almobadara.backend.repository.family.FamilyMemberRepository;
import ma.almobadara.backend.service.beneficiary.AddedBeneficiaryResponse;
import ma.almobadara.backend.service.beneficiary.BeneficiaryService;
import ma.almobadara.backend.service.beneficiary.EducationService;
import ma.almobadara.backend.service.family.FamilyMemberService;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static ma.almobadara.backend.service.migration.util.UtilityClass.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class BeneficiaryActifFamilyMigrationService {
    private final FamilyMemberService familyMemberService;
    private final BeneficiaryService beneficiaryService;
    private final BeneficiaryRepository benenficiaryRepository;
    private final PersonRepository personRepository;
    private final EducationService educationService;
    private final FamilyMemberRepository familyMemberRepository;
    private static final Long RELATIONSHIP_FATHER = 1L;
    private static final Long RELATIONSHIP_MOTHER = 2L;
    private static final Long RELATIONSHIP_SON = 3L;
    private final RefFeignClient refFeignClient;
    private static final Long RELATIONSHIP_DAUGHTER = 4L;
    private final TagRepository tagRepository;
    private final Map<String, Long> cat = Map.of(
            "ET",1L,
            "OR", 2L,
            "HA", 4L,
            "VE", 3L,
            "FA",5L,
            "EN",6L);


    @Transactional(rollbackFor = Exception.class) // this enables rollback on any exception
    public void migrateBeneficiaryFamily(MultipartFile file ,
                                            Long zoneId,
                                            Long cityId,
                                            Long beneficiaryCategoryId,
                                            Long beneficiaryStatusId) throws IOException, FunctionalException {
        log.info("Request to migrateBeneficiaryFamily : {}", file);
        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {

            Sheet sheet = workbook.getSheetAt(0);
            for (Row row : sheet) {
                if (row.getRowNum() == 0) continue;
                if (row.getCell(0) == null || row.getCell(0).getCellType() == CellType.BLANK) {
                    break;
                }
                String code =getCellValue(row,1);
                Optional<Beneficiary> beneficiary=benenficiaryRepository.findByAccountingCode(code);
                if(beneficiary.isPresent()){
                    continue;
                }
                // befire the creation of teh family part we shold check if itjust a bother so inthis case we has to begin from teh addnchildren part
                FamilyMember tutorFamilyMember = familyMemberRepository.findByFirstIdentityCode(getCellValue(row, 18));
                if (tutorFamilyMember != null) {
                    List<FamilyMemberAddDTO> childrenFamilyMemberAddDTO = createChildrenInformation(row, tutorFamilyMember.getFamily().getId());
                    familyMemberService.addListOfFamilyMembers(childrenFamilyMemberAddDTO);
                    BeneficiaryAddDTO beneficiaryAddDTO = createBeneficiaryPersonalInformation(row , beneficiaryCategoryId, beneficiaryStatusId , zoneId);

                    AddedBeneficiaryResponse beneficiaryAddDTO1=beneficiaryService.addBeneficiary(beneficiaryAddDTO);
                    if(getCellValue(row,10)!=null && !Objects.equals(getCellValue(row, 10), "")){
                        BeneficiaryEducation beneficiaryEducation=createEducationInfos(row,beneficiaryAddDTO1.getId());
                        beneficiaryService.addEducationToBeneficiary(beneficiaryEducation);
                    }
                    continue;
                }
                FamilyMemberAddDTO familyMemberAddDTO = createGeneralFamilyInformation(row , zoneId, cityId);
                AddedFamilyMemberResponse familyMemberResponse = familyMemberService.addFamilyMember(familyMemberAddDTO);

                FamilyMemberAddDTO fatherFamilyMemberAddDTO = createFatherInformation(row, familyMemberResponse.getFamilyId());
                familyMemberService.addFamilyMember(fatherFamilyMemberAddDTO);

                FamilyMemberAddDTO motherFamilyMemberAddDTO = createMotherInformation(row, familyMemberResponse.getFamilyId());
                familyMemberService.addFamilyMember(motherFamilyMemberAddDTO);

                List<FamilyMemberAddDTO> childrenFamilyMemberAddDTO = createChildrenInformation(row, familyMemberResponse.getFamilyId());
                familyMemberService.addListOfFamilyMembers(childrenFamilyMemberAddDTO);

                try {
                    BeneficiaryAddDTO beneficiaryAddDTO = createBeneficiaryPersonalInformation(row , beneficiaryCategoryId, beneficiaryStatusId , zoneId);

                    AddedBeneficiaryResponse beneficiaryAddDTO1=beneficiaryService.addBeneficiary(beneficiaryAddDTO);
                    System.out.println(getCellValue(row,10));
                    if(getCellValue(row,10)!=null && !Objects.equals(getCellValue(row, 10), "")){
                        BeneficiaryEducation beneficiaryEducation=createEducationInfos(row,beneficiaryAddDTO1.getId());
                        beneficiaryService.addEducationToBeneficiary(beneficiaryEducation);
                    }
                }catch (Exception e){
                    log.warn("Failed to add beneficiary: {}", e.getMessage());
                }

            }
        } catch (TechnicalException e) {
            log.error("Error during migration: {}", e.getMessage());
            throw new IOException("Error during migration: " + e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }
    private List<TagDTO> buildTags() {
        List<TagDTO> tags = new ArrayList<>();
        tagRepository.findByNameAndType("migration", "metier").ifPresent(tag -> tags.add(TagDTO.builder().id(tag.getId()).name(tag.getName()).build()));
        tagRepository.findByNameAndType("à_compléter", "metier").ifPresent(tag -> tags.add(TagDTO.builder().id(tag.getId()).name(tag.getName()).build()));
        return tags;
    }
    // first step is teh creation of the family
     //step 1.1  create family without mmembers
    private BeneficiaryEducation createEducationInfos(Row row,Long beneficiaryId) throws TechnicalException {
        BeneficiaryEducation educationDTO=BeneficiaryEducation.builder()
                .schoolName(getTextCellValue(row,22))
                 .beneficiaryId(beneficiaryId)
                .educated(true)
                .schoolNameAr(getArabicTextCellValue(row,21)).build();
        if(getCellValue(row, 10) == null || getCellValue(row, 10).isEmpty() || getCellValue(row, 10).equals("0") || getCellValue(row, 10).contains("*") ||getNumericCellValue(row,10)== 0L) {
            log.warn("School level is missing for beneficiary with ID: {}", beneficiaryId);
            return null; // Skip this row if school level is not provided
        }
         educationDTO.setSchoolLevelId(getNumericCellValue(row,10));
        return educationDTO;
    }
    // Helper methods for default values
    private String getTextCellValue(Row row, int idx) {
        String value = getCellValue(row, idx);
        return (value == null || value.trim().isEmpty()) ? "Inconnu" : value;
    }
    private String getArabicTextCellValue(Row row, int idx) {
        String value = getCellValue(row, idx);
        return (value == null || value.trim().isEmpty()) ? "غير معروف" : value;
    }
    private Long getNumericCellValue(Row row, int idx) {
        String value = getCellValue(row, idx);
        try {
            return (value == null || value.trim().isEmpty()) ? 0L : Long.parseLong(value);
        } catch (NumberFormatException e) {
            return 0L;
        }
    }
    private String getDateCellValue(Row row, int idx) {
        String value = getCellValue(row, idx);
        return (value == null || value.trim().isEmpty()) ? "01/01/2024" : value;
    }
    // first step is teh creation of the family
     //step 1.1  create family without mmembers
    private FamilyMemberAddDTO createGeneralFamilyInformation(Row row , Long zoneId, Long cityId) {
        FamilyMemberAddDTO familyMemberAddDTO = new FamilyMemberAddDTO();
        familyMemberAddDTO.setCityId(cityId);
        familyMemberAddDTO.setAddress(getTextCellValue(row, 44));
        familyMemberAddDTO.setAddressAr(getArabicTextCellValue(row, 43));
        familyMemberAddDTO.setPhoneNumber(getTextCellValue(row, 34));
        List<TagDTO> tags = buildTags();
        familyMemberAddDTO.setAccommodationNatureId(4L);
        familyMemberAddDTO.setAccommodationTypeId(10L);
        familyMemberAddDTO.setTags(tags);
        familyMemberAddDTO.setZoneId(zoneId);
        familyMemberAddDTO.setGeneralComment("المعلومات العامة حول الإيواء هي كالتالي: " + getTextCellValue(row, 39) + " " + getTextCellValue(row, 40) + " " + getTextCellValue(row, 41));
        return familyMemberAddDTO;
    }

    //step 1.2 create the father info

    private FamilyMemberAddDTO createFatherInformation(Row row, Long familyId) throws TechnicalException {
        FamilyMemberAddDTO familyMemberAddDTO = new FamilyMemberAddDTO();
        familyMemberAddDTO.setFamilyId(familyId);
        familyMemberAddDTO.setFamilyRelationshipId(RELATIONSHIP_FATHER);
        String[] nameParts =splitName(getTextCellValue(row, 25));
        familyMemberAddDTO.setFirstName(nameParts[0]);
        familyMemberAddDTO.setLastName(nameParts[1]);
        String[] namePartsAr = splitName(getArabicTextCellValue(row, 24));
        familyMemberAddDTO.setFirstNameAr(namePartsAr[0]);
        familyMemberAddDTO.setLastNameAr(namePartsAr[1]);
        familyMemberAddDTO.setBirthDate(convertStringToDate(getDateCellValue(row, 26)));
        familyMemberAddDTO.setSex("Homme");
        familyMemberAddDTO.setDeceased(true);
        familyMemberAddDTO.setProfessionId(35L);
        familyMemberAddDTO.setDeathReasonId(8L);
        familyMemberAddDTO.setGeneralComment("توفي الأب بتاريخ " + getDateCellValue(row, 27) + " وسبب الوفاة هو " + getTextCellValue(row, 29));
        familyMemberAddDTO.setDeathDate(convertStringToDate(getDateCellValue(row, 27)));
        return familyMemberAddDTO;
    }


    //step 1.3 create the mother info

    private FamilyMemberAddDTO createMotherInformation(Row row, Long familyId) throws TechnicalException {
        FamilyMemberAddDTO familyMemberAddDTO = new FamilyMemberAddDTO();
        familyMemberAddDTO.setFamilyId(familyId);
        familyMemberAddDTO.setFamilyRelationshipId(RELATIONSHIP_MOTHER);
        familyMemberAddDTO.setTutor(true);

        familyMemberAddDTO.setTutorStartDate(convertStringToDate(getDateCellValue(row, 4)));
        String[] nameParts = splitName(getTextCellValue(row, 30));
        familyMemberAddDTO.setFirstName(nameParts[0]);
        familyMemberAddDTO.setLastName(nameParts[1]);
        String[] namePartsAr = splitName(getArabicTextCellValue(row, 29));
        familyMemberAddDTO.setFirstNameAr(namePartsAr[0]);
        familyMemberAddDTO.setLastNameAr(namePartsAr[1]);
        familyMemberAddDTO.setProfessionId(35L);

        familyMemberAddDTO.setBirthDate(convertStringToDate(getDateCellValue(row, 32)));
        familyMemberAddDTO.setIdentityCode(getTextCellValue(row, 31));
        familyMemberAddDTO.setSex("Femme");
        familyMemberAddDTO.setGeneralComment("الوالدة تحمل هوية رقم " + getTextCellValue(row, 31) + " وتعمل في مهنة " + getTextCellValue(row, 35));
        return familyMemberAddDTO;
    }

    // step 1.4 create the children info it gonne just one by line and it the one gonne be the beneficary

    private List<FamilyMemberAddDTO> createChildrenInformation(Row row, Long familyId) throws TechnicalException {
        List<FamilyMemberAddDTO> familyMemberAddDTOList = new ArrayList<>();
        FamilyMemberAddDTO familyMemberAddDTO = new FamilyMemberAddDTO();
        familyMemberAddDTO.setFamilyId(familyId);
        String genderCell = getArabicTextCellValue(row, 5).equals("ذكر") ? "Homme" : "Femme";
        if ("Femme".equals(genderCell)) {
            familyMemberAddDTO.setFamilyRelationshipId(RELATIONSHIP_DAUGHTER);
        } else {
            familyMemberAddDTO.setFamilyRelationshipId(RELATIONSHIP_SON);
        }
        String[] nameParts = splitName(getTextCellValue(row, 3));
        familyMemberAddDTO.setFirstName(nameParts[0]);
        familyMemberAddDTO.setLastName(nameParts[1]);
        familyMemberAddDTO.setAddress(getTextCellValue(row, 44));
        familyMemberAddDTO.setAddressAr(getArabicTextCellValue(row, 43));
        familyMemberAddDTO.setBirthDate(convertStringToDate(getDateCellValue(row, 4)));

        String[] namePartsAr = splitName(getArabicTextCellValue(row, 2));
        familyMemberAddDTO.setFirstNameAr(namePartsAr[0]);
        familyMemberAddDTO.setLastNameAr(namePartsAr[1]);
        familyMemberAddDTOList.add(familyMemberAddDTO);
        return familyMemberAddDTOList;
    }

    // second Step creation of the Beneficiary

    // step 1.1 enter personal incofrmation
    private BeneficiaryAddDTO createBeneficiaryPersonalInformation(Row row, Long beneficiaryCategoryId, Long beneficiaryStatusId, Long zoneId) throws Exception {
        BeneficiaryAddDTO beneficiaryAddDTO = new BeneficiaryAddDTO();
        String[] nameParts = splitName(getTextCellValue(row, 3));

        Person person = personRepository.findByFirstNameAndLastNameAndBirthDate(nameParts[0], nameParts[1], convertStringToDate(getDateCellValue(row, 4)));

        if (person == null) {
            throw new TechnicalException("No person found for name: " + nameParts[0] + " " + nameParts[1]);
        }
        // Set person information
        beneficiaryAddDTO.setPersonId(person.getId());
        beneficiaryAddDTO.setIndependent(false);
        beneficiaryAddDTO.setTags(buildTags());
        beneficiaryAddDTO.setArchived(false);
        beneficiaryAddDTO.setAddress(getTextCellValue(row, 44));
        beneficiaryAddDTO.setAddressAr(getArabicTextCellValue(row, 43));
        beneficiaryAddDTO.setOldBeneficiary(true);

        beneficiaryAddDTO.setAccountingCode(getTextCellValue(row, 1));
        beneficiaryAddDTO.setFirstName(nameParts[0]);
        beneficiaryAddDTO.setLastName(nameParts[1]);
        String[] namePartsAr = splitName(getArabicTextCellValue(row, 2));
        beneficiaryAddDTO.setFirstNameAr(namePartsAr[0]);
        beneficiaryAddDTO.setLastNameAr(namePartsAr[1]);
        beneficiaryAddDTO.setSex(getTextCellValue(row, 5));
        beneficiaryAddDTO.setBirthDate(convertStringToDate(getDateCellValue(row, 4)));
        // Set additional details
        beneficiaryAddDTO.setZoneId(zoneId);
        beneficiaryAddDTO.setRemarqueAr(getArabicTextCellValue(row, 51));
        if(beneficiaryCategoryId!=null){
            beneficiaryAddDTO.setCategoryBeneficiaryId(beneficiaryCategoryId);

        }
        else{
            String category=getTextCellValue(row,1).substring(0,2);
            beneficiaryAddDTO.setCategoryBeneficiaryId(cat.getOrDefault(category, 0L));

        }
        beneficiaryAddDTO.setBeneficiaryStatusId(beneficiaryStatusId);
        String typeKafalat = getTextCellValue(row, 35);
        beneficiaryAddDTO.setTypeKafalatId(typeKafalat.startsWith("eps") ? 1L : 2L);

        String addedDateStr = getDateCellValue(row, 48);
        if (addedDateStr != null && !addedDateStr.trim().isEmpty()) {
            addedDateStr = addedDateStr.trim();

            // Dictionnaire des mois arabes -> numéro de mois
            Map<String, String> arabicMonths = Map.ofEntries(
                    Map.entry("يناير", "01"),
                    Map.entry("فبراير", "02"),
                    Map.entry("مارس", "03"),
                    Map.entry("أبريل", "04"),
                    Map.entry("ماي", "05"),
                    Map.entry("يونيو", "06"),
                    Map.entry("يوليوز", "07"),
                    Map.entry("غشت", "08"),
                    Map.entry("شتنبر", "09"),
                    Map.entry("أكتوبر", "10"),
                    Map.entry("نونبر", "11"),
                    Map.entry("دجنبر", "12")
            );

            try {
                LocalDate addedDate;

                // Cas 1: Format standard "dd/MM/yyyy"
                if (addedDateStr.matches("\\d{2}/\\d{2}/\\d{4}")) {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
                    addedDate = LocalDate.parse(addedDateStr, formatter);
                } else {
                    // Cas 2 et 3 : Mois arabe + année
                    String month = null;
                    String year = null;

                    for (String arabicMonth : arabicMonths.keySet()) {
                        if (addedDateStr.contains(arabicMonth)) {
                            month = arabicMonths.get(arabicMonth);
                            year = addedDateStr.replace(arabicMonth, "").replace("/", "").replace(" ", "").trim();
                            break;
                        }
                    }

                    if (month != null && year != null && year.matches("\\d{4}")) {
                        // On crée une date au premier du mois
                        String formatted = "01/" + month + "/" + year;
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
                        addedDate = LocalDate.parse(formatted, formatter);
                    } else {
                        throw new TechnicalException("Format de date invalide (mois arabe non reconnu ou année manquante)");
                    }
                }

                beneficiaryAddDTO.setAddedYear(addedDate.toString()); // yyyy-MM-dd
            } catch (Exception e) {
                throw new TechnicalException("Invalid added date format in row " + row.getRowNum() + ": " + addedDateStr);
            }
        } else {
            beneficiaryAddDTO.setAddedYear(null);
        }
        return beneficiaryAddDTO;
    }


    private String[] splitName(String fullName) {
        if (fullName == null || fullName.isEmpty()) return new String[]{"", ""};
        String[] parts = fullName.split(" ", 2);
        return new String[]{parts.length > 0 ? parts[0] : "", parts.length > 1 ? parts[1] : ""};
    }


    @Transactional(rollbackFor = Exception.class) // this enables rollback on any exception
    public void migrateBeneficiaryEducation(MultipartFile file  ,Long cityId) throws IOException, FunctionalException {
        log.info("Request to migrateBeneficiaryFamily : {}", file);
        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {

            Sheet sheet = workbook.getSheetAt(0);
            for (Row row : sheet) {
                if (row.getRowNum() == 0) continue;
                if (row.getCell(0) == null || row.getCell(0).getCellType() == CellType.BLANK) {
                    break;
                }
                String code =getCellValue(row,1);
                Optional<Beneficiary> beneficiary=benenficiaryRepository.findByAccountingCode(code);
                if(!beneficiary.isPresent()){
                    continue;
                }
                createEducation(row,beneficiary.get(),cityId);

            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    private void createEducation(Row row, Beneficiary beneficiary,Long cityId) {
        Map<Integer, Long> reversedMap = new HashMap<>();

        reversedMap.put(12, 6L);
        reversedMap.put(13, 7L);
        reversedMap.put(14, 7L);
        reversedMap.put(15, 8L);
        reversedMap.put(16, 8L);
        reversedMap.put(17, 9L);
        reversedMap.put(18, 9L);
        reversedMap.put(19, 10L);
        reversedMap.put(20, 10L);
        int s= 2;
        for (int i = 12; i < 20; i++) {
            try{
                EducationDTO educationDTO = new EducationDTO();
                BeneficiaryDTO beneficiaryDTO = new BeneficiaryDTO();
                beneficiaryDTO.setId(beneficiary.getId());
                educationDTO.setBeneficiary(beneficiaryDTO);
                educationDTO.setSchoolName(getTextCellValue(row,22));
                educationDTO.setSchoolNameAr(getTextCellValue(row,21));
                if(getCellValue(row, 10) == null || getCellValue(row, 10).isEmpty() || getCellValue(row, 10).equals("0") || getCellValue(row, 10).contains("*") ) {
                    log.warn("School level is missing for beneficiary with code: {}", beneficiary.getAccountingCode());
                    return; // Skip this row if school level is not provided
                }
                SchoolLevelDTO schoolLevelDTO = refFeignClient.getParSchoolLevel(getNumericCellValue(row, 10));

                educationDTO.setSchoolLevel(SchoolLevelDTO.builder().id(getNumericCellValue(row, 10)).build());
                educationDTO.setEducationType(schoolLevelDTO.getType());
                educationDTO.setEducationSystemType(EducationSystemTypeDTO.builder().id(2L).build());
                educationDTO.setStudentNumber("Inconnu");
                educationDTO.setCity(CityDTO.builder().id(cityId).build());
                educationDTO.setSucceed(Boolean.TRUE);
                educationDTO.setHonor(HonorDTO.builder().id(2L).build());
                System.out.println("Cell " + i + ": " + getCellValue(row, i));
                educationDTO.setSemestre("Semestre " + s);
                if (reversedMap.containsKey(i)) {
                    educationDTO.setSchoolYear(SchoolYearDTO.builder().id(reversedMap.get(i)).build());
                } else {
                    log.warn("No school year mapping found for cell index: {}", i);
                    continue; // Skip if no mapping is found
                }
                if(cleanNote(getCellValue(row, i))==0){
                    continue;
                }
                Double mark = BigDecimal
                        .valueOf(Double.valueOf(cleanNote(getCellValue(row, i))))
                        .setScale(2, RoundingMode.HALF_UP)
                        .doubleValue();
                educationDTO.setMark(mark);
                educationService.addEducationToBeneficiary(beneficiary, educationDTO);
                s=s==2? 1:2;
            }catch (Exception e){
                log.error("Error while adding education for beneficiary {}: {}", beneficiary.getId(), e.getMessage());
            }

        }

       // educationDTO.setComment("commentaire");
    }
    }

package ma.almobadara.backend.service.migration.util;

import lombok.extern.slf4j.Slf4j;
import ma.almobadara.backend.exceptions.TechnicalException;
import org.apache.poi.ss.usermodel.Row;

import java.text.SimpleDateFormat;
import java.util.Date;

@Slf4j
public class UtilityClass {

    public static String getCellValue(Row row, int cellIndex) {
        try {
            if (row.getCell(cellIndex) != null) {
                switch (row.getCell(cellIndex).getCellType()) {
                    case STRING:
                        return row.getCell(cellIndex).getStringCellValue();
                    case NUMERIC:
                        // Check if the cell contains a date (numeric type)
                        if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(row.getCell(cellIndex))) {
                            // Convert the numeric date to a string formatted as dd/MM/yyyy
                            return new SimpleDateFormat("dd/MM/yyyy").format(row.getCell(cellIndex).getDateCellValue());
                        } else {
                            // Use BigDecimal to avoid scientific notation
                            java.math.BigDecimal bd = new java.math.BigDecimal(row.getCell(cellIndex).getNumericCellValue());
                            return bd.stripTrailingZeros().toPlainString();
                        }
                    case BOOLEAN:
                        return String.valueOf(row.getCell(cellIndex).getBooleanCellValue());
                    case FORMULA:
                        return row.getCell(cellIndex).getCellFormula();
                    default:
                        return "";
                }
            }
        } catch (Exception e) {
            log.error("Error getting cell value from row {} at index {}", row.getRowNum(), cellIndex, e);
        }
        return "";
    }

    public static Date convertStringToDate(String dateString) throws TechnicalException {
        if (dateString == null || dateString.trim().isEmpty()) {
            return null; // Return null for empty values
        }

        SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
        dateFormat.setLenient(false); // Ensures strict parsing

        try {
            return dateFormat.parse(dateString);
        } catch (java.text.ParseException e) {
            // Log the invalid date and return a default date instead of throwing exception
            System.out.println("Warning: Invalid date format '" + dateString + "', using default date 01/01/2024");
            try {
                return dateFormat.parse("01/01/2024");
            } catch (java.text.ParseException defaultParseException) {
                throw new TechnicalException("Failed to parse default date: " + defaultParseException.getMessage());
            }
        }
    }

        public static Float cleanNote(String rawNote) {
            if (rawNote == null || rawNote.trim().isEmpty() || rawNote.trim().equals("****") || rawNote.trim().equals("***")) {
                return null;
            }

            // Remplace ; par , pour unifier les séparateurs
            String cleanedNote = rawNote.trim().replace(";", ",");

            try {
                // Cas avec /10
                if (cleanedNote.contains("/10")) {
                    String[] splitSlash = cleanedNote.split("/10");
                    String beforeSlash = splitSlash[0].trim();
                    String[] parts = beforeSlash.split(",");

                    String combined = combineAsFloatString(parts);
                    float note = Float.parseFloat(combined);
                    return (note <= 10f) ? note : null;
                }

                // Cas avec /20
                if (cleanedNote.contains("/20")) {
                    String[] splitSlash = cleanedNote.split("/20");
                    String beforeSlash = splitSlash[0].trim();
                    String[] parts = beforeSlash.split(",");

                    String combined = combineAsFloatString(parts);
                    float note = Float.parseFloat(combined) / 2f;
                    return (note <= 10f) ? note : null;
                }

                // Cas simple (pas de /)
                String[] parts = cleanedNote.split(",");
                float note = Float.parseFloat(parts[0].trim());

                if (note < 10f) return note;
                if (note < 20f) return note / 2f;
                return null;

            } catch (Exception e) {
                return null;
            }
        }

        // Combine "08" and "21" → "8.21"
        private static String combineAsFloatString(String[] parts) {
            if (parts.length >= 2) {
                String part1 = parts[0].trim().replaceFirst("^0+(?!$)", ""); // remove leading zero
                String part2 = parts[1].trim();
                return part1 + "." + part2;
            } else {
                return parts[0].trim();
            }
        }

}
